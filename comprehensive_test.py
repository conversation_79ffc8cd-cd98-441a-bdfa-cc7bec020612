#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Comprehensive Bot Test
Test all bot functionalities
"""

import asyncio
from pyrogram import Client
from decouple import config

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                COMPREHENSIVE BOT TEST                        ║
║              Save Restricted Content Bot                     ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

async def test_bot_comprehensive():
    """اختبار شامل للبوت"""
    print_banner()
    
    try:
        # قراءة المتغيرات
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        BOT_TOKEN = config("BOT_TOKEN")
        SESSION = config("SESSION")
        FORCESUB = config("FORCESUB", default="")
        AUTH = config("AUTH", cast=int)
        
        print(f"{Colors.YELLOW}🔄 بدء الاختبار الشامل...{Colors.END}")
        
        # اختبار البوت
        print(f"\n{Colors.BLUE}{Colors.BOLD}🤖 اختبار البوت:{Colors.END}")
        bot_client = Client("test_bot", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)
        
        async with bot_client:
            me = await bot_client.get_me()
            print(f"{Colors.GREEN}✅ البوت يعمل: @{me.username}{Colors.END}")
            
            # اختبار القناة الإجبارية
            if FORCESUB:
                try:
                    channel = await bot_client.get_chat(FORCESUB)
                    print(f"{Colors.GREEN}✅ القناة الإجبارية متاحة: @{FORCESUB} ({channel.members_count} عضو){Colors.END}")
                except Exception as e:
                    print(f"{Colors.RED}❌ مشكلة في القناة الإجبارية: {str(e)}{Colors.END}")
        
        # اختبار المستخدم
        print(f"\n{Colors.BLUE}{Colors.BOLD}👤 اختبار المستخدم:{Colors.END}")
        userbot = Client("test_user", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            me = await userbot.get_me()
            print(f"{Colors.GREEN}✅ المستخدم متصل: {me.first_name} (@{me.username or 'لا يوجد'}){Colors.END}")
            
            # اختبار الوصول للقنوات العامة
            test_channels = ["telegram", "durov"]
            accessible_channels = []
            
            for channel in test_channels:
                try:
                    chat = await userbot.get_chat(channel)
                    accessible_channels.append(channel)
                    print(f"{Colors.GREEN}✅ يمكن الوصول لـ @{channel}: {chat.title}{Colors.END}")
                except Exception as e:
                    print(f"{Colors.YELLOW}⚠️ لا يمكن الوصول لـ @{channel}: {str(e)}{Colors.END}")
            
            # اختبار قراءة الرسائل
            if accessible_channels:
                test_channel = accessible_channels[0]
                try:
                    messages = []
                    async for message in userbot.get_chat_history(test_channel, limit=1):
                        messages.append(message)
                    if messages:
                        print(f"{Colors.GREEN}✅ يمكن قراءة الرسائل من @{test_channel}{Colors.END}")
                    else:
                        print(f"{Colors.YELLOW}⚠️ لا توجد رسائل في @{test_channel}{Colors.END}")
                except Exception as e:
                    print(f"{Colors.RED}❌ لا يمكن قراءة الرسائل: {str(e)}{Colors.END}")
        
        # اختبار الإعدادات
        print(f"\n{Colors.BLUE}{Colors.BOLD}⚙️ اختبار الإعدادات:{Colors.END}")
        print(f"{Colors.CYAN}📊 API_ID: {API_ID}{Colors.END}")
        print(f"{Colors.CYAN}📊 API_HASH: {API_HASH[:10]}...{Colors.END}")
        print(f"{Colors.CYAN}📊 BOT_TOKEN: {BOT_TOKEN[:20]}...{Colors.END}")
        print(f"{Colors.CYAN}📊 SESSION: {SESSION[:20]}...{Colors.END}")
        print(f"{Colors.CYAN}📊 AUTH: {AUTH}{Colors.END}")
        print(f"{Colors.CYAN}📊 FORCESUB: {FORCESUB or 'غير مفعل'}{Colors.END}")
        
        # نصائح للاستخدام
        print(f"\n{Colors.BLUE}{Colors.BOLD}💡 نصائح للاستخدام:{Colors.END}")
        print(f"{Colors.YELLOW}🔹 للقنوات العامة: أرسل الرابط مباشرة{Colors.END}")
        print(f"{Colors.YELLOW}🔹 للقنوات الخاصة: انضم للقناة أولاً بحسابك{Colors.END}")
        print(f"{Colors.YELLOW}🔹 للبوتات: استخدم صيغة t.me/b/bot_username/message_id{Colors.END}")
        print(f"{Colors.YELLOW}🔹 للحفظ المتعدد: استخدم /batch (للمالك فقط){Colors.END}")
        
        return True
        
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ في الاختبار: {str(e)}{Colors.END}")
        return False

async def test_specific_link():
    """اختبار رابط معين"""
    print(f"\n{Colors.BLUE}{Colors.BOLD}🔗 اختبار رابط معين:{Colors.END}")
    
    # روابط للاختبار
    test_links = [
        "https://t.me/durov/1",  # قناة عامة
        "https://t.me/telegram/1",  # قناة تليجرام الرسمية
    ]
    
    print(f"{Colors.YELLOW}أدخل رابط للاختبار (أو اتركه فارغ لاستخدام الافتراضي):{Colors.END}")
    user_link = input(f"{Colors.WHITE}الرابط: {Colors.END}").strip()
    
    if user_link:
        test_links.insert(0, user_link)
    
    API_ID = config("API_ID", cast=int)
    API_HASH = config("API_HASH")
    SESSION = config("SESSION")
    
    userbot = Client("test_link", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
    
    async with userbot:
        for link in test_links[:1]:  # اختبار رابط واحد فقط
            try:
                print(f"{Colors.YELLOW}🔄 اختبار: {link}{Colors.END}")
                
                # استخراج معلومات الرابط
                if '/c/' in link:
                    # قناة خاصة
                    parts = link.split('/')
                    channel_id = int(f"-100{parts[-2]}")
                    msg_id = int(parts[-1])
                elif '/b/' in link:
                    # بوت
                    parts = link.split('/')
                    bot_username = parts[-2]
                    msg_id = int(parts[-1])
                    print(f"{Colors.CYAN}📊 بوت: @{bot_username}, رسالة: {msg_id}{Colors.END}")
                    continue
                else:
                    # قناة عامة
                    parts = link.split('/')
                    channel_username = parts[-2]
                    msg_id = int(parts[-1])
                    channel_id = channel_username
                
                # محاولة الوصول للرسالة
                message = await userbot.get_messages(channel_id, msg_id)
                if message:
                    print(f"{Colors.GREEN}✅ تم العثور على الرسالة!{Colors.END}")
                    print(f"{Colors.CYAN}📊 النوع: {message.media or 'نص'}{Colors.END}")
                    print(f"{Colors.CYAN}📊 المحتوى: {(message.text or message.caption or 'لا يوجد نص')[:50]}...{Colors.END}")
                    
                    if message.media:
                        print(f"{Colors.GREEN}✅ يمكن تحميل هذا الملف!{Colors.END}")
                    else:
                        print(f"{Colors.YELLOW}⚠️ رسالة نصية فقط{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ لم يتم العثور على الرسالة{Colors.END}")
                    
                break
                
            except Exception as e:
                print(f"{Colors.RED}❌ خطأ في الرابط {link}: {str(e)}{Colors.END}")
                continue

def main():
    """الدالة الرئيسية"""
    try:
        # الاختبار الشامل
        success = asyncio.run(test_bot_comprehensive())
        
        if success:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 الاختبار الشامل نجح!{Colors.END}")
            
            # اختبار رابط معين
            test_link = input(f"\n{Colors.YELLOW}هل تريد اختبار رابط معين؟ (y/n): {Colors.END}").strip().lower()
            if test_link in ['y', 'yes', 'نعم']:
                asyncio.run(test_specific_link())
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}❌ فشل الاختبار الشامل!{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء الاختبار{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
