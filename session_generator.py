#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Session Generator for Save Restricted Content Bot
Created by: Assistant
"""

import asyncio
from pyrogram import Client
import os

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    SESSION GENERATOR                         ║
║              Save Restricted Content Bot                     ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def get_api_credentials():
    """الحصول على API_ID و API_HASH"""
    print(f"{Colors.YELLOW}📋 أدخل بيانات API من my.telegram.org:{Colors.END}\n")
    
    while True:
        try:
            api_id = input(f"{Colors.BLUE}🔢 API_ID: {Colors.END}")
            api_id = int(api_id.strip())
            break
        except ValueError:
            print(f"{Colors.RED}❌ خطأ: API_ID يجب أن يكون رقم صحيح!{Colors.END}")
    
    api_hash = input(f"{Colors.BLUE}🔐 API_HASH: {Colors.END}").strip()
    
    if not api_hash:
        print(f"{Colors.RED}❌ خطأ: API_HASH لا يمكن أن يكون فارغ!{Colors.END}")
        return get_api_credentials()
    
    return api_id, api_hash

def get_phone_number():
    """الحصول على رقم الهاتف"""
    print(f"\n{Colors.YELLOW}📱 أدخل رقم هاتفك:{Colors.END}")
    print(f"{Colors.CYAN}💡 مثال: +966501234567 أو +201234567890{Colors.END}")
    
    phone = input(f"{Colors.BLUE}📞 رقم الهاتف: {Colors.END}").strip()
    
    if not phone.startswith('+'):
        phone = '+' + phone
    
    return phone

async def generate_session():
    """إنشاء جلسة Pyrogram"""
    print_banner()
    
    try:
        # الحصول على البيانات
        api_id, api_hash = get_api_credentials()
        phone = get_phone_number()
        
        print(f"\n{Colors.YELLOW}🔄 جاري إنشاء الجلسة...{Colors.END}")
        
        # إنشاء عميل Pyrogram
        async with Client("session_generator", api_id=api_id, api_hash=api_hash, phone_number=phone) as app:
            print(f"{Colors.GREEN}✅ تم الاتصال بنجاح!{Colors.END}")
            
            # الحصول على معلومات المستخدم
            me = await app.get_me()
            print(f"{Colors.CYAN}👤 مرحباً {me.first_name}!{Colors.END}")
            print(f"{Colors.CYAN}🆔 معرف المستخدم: {me.id}{Colors.END}")
            
            # الحصول على session string
            session_string = await app.export_session_string()
            
            # حفظ الجلسة في ملف
            with open("session.txt", "w", encoding="utf-8") as f:
                f.write(session_string)
            
            # عرض النتائج
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 تم إنشاء الجلسة بنجاح!{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            print(f"{Colors.YELLOW}📄 SESSION STRING:{Colors.END}")
            print(f"{Colors.WHITE}{session_string}{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            
            print(f"\n{Colors.CYAN}💾 تم حفظ الجلسة في ملف: session.txt{Colors.END}")
            print(f"{Colors.CYAN}🆔 معرف المستخدم (AUTH): {me.id}{Colors.END}")
            
            # إنشاء ملف المتغيرات
            env_content = f"""# متغيرات البوت
API_ID={api_id}
API_HASH={api_hash}
BOT_TOKEN=8055516959:AAH39KLCvBHVzVjed9DJXt7naxhsAltNx-w
SESSION={session_string}
AUTH={me.id}
FORCESUB=premuimfreex
"""
            
            with open(".env", "w", encoding="utf-8") as f:
                f.write(env_content)
            
            print(f"{Colors.GREEN}✅ تم إنشاء ملف .env بجميع المتغيرات!{Colors.END}")
            
            return session_string, me.id
            
    except Exception as e:
        print(f"{Colors.RED}❌ حدث خطأ: {str(e)}{Colors.END}")
        return None, None

def main():
    """الدالة الرئيسية"""
    try:
        # تشغيل المولد
        session, user_id = asyncio.run(generate_session())
        
        if session:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🚀 البوت جاهز للتشغيل!{Colors.END}")
            print(f"{Colors.YELLOW}📋 استخدم المتغيرات من ملف .env{Colors.END}")
        else:
            print(f"{Colors.RED}❌ فشل في إنشاء الجلسة{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء العملية بواسطة المستخدم{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
