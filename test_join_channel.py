#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Channel Join and Access
"""

import asyncio
from pyrogram import Client
from pyrogram.errors import FloodWait, InviteHashInvalid, InviteHashExpired, UserAlreadyParticipant
from decouple import config

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

async def test_join_and_access():
    """اختبار الانضمام والوصول للقناة"""
    
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                 JOIN & ACCESS TEST                           ║")
    print("║              Test Channel Join and Message Access            ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print(f"{Colors.END}")
    
    try:
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        userbot = Client("test_join", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            me = await userbot.get_me()
            print(f"{Colors.BLUE}🤖 الحساب: {me.first_name} (@{me.username or 'لا يوجد'}){Colors.END}")
            
            # رابط الدعوة
            invite_link = "https://t.me/+q6xNhIeUgSViMmJk"
            print(f"\n{Colors.YELLOW}🔗 رابط الدعوة: {invite_link}{Colors.END}")
            
            # محاولة الانضمام
            try:
                chat = await userbot.join_chat(invite_link)
                print(f"{Colors.GREEN}✅ تم الانضمام بنجاح!{Colors.END}")
                print(f"{Colors.CYAN}📊 اسم القناة: {chat.title}{Colors.END}")
                print(f"{Colors.CYAN}📊 معرف القناة: {chat.id}{Colors.END}")
                print(f"{Colors.CYAN}📊 نوع القناة: {chat.type}{Colors.END}")
                
                # حفظ معرف القناة
                channel_id = chat.id
                
            except UserAlreadyParticipant:
                print(f"{Colors.GREEN}✅ المستخدم منضم مسبقاً{Colors.END}")
                
                # البحث عن القناة في القائمة
                channel_id = None
                async for dialog in userbot.get_dialogs(limit=100):
                    if dialog.chat.type.name == "CHANNEL" and dialog.chat.username is None:
                        # قناة خاصة - تحقق من الرابط
                        try:
                            # محاولة الحصول على رابط دعوة للمقارنة
                            if str(dialog.chat.id).endswith("2349294062"):
                                channel_id = dialog.chat.id
                                print(f"{Colors.GREEN}✅ تم العثور على القناة: {dialog.chat.title}{Colors.END}")
                                print(f"{Colors.CYAN}📊 معرف القناة: {channel_id}{Colors.END}")
                                break
                        except:
                            continue
                
                if not channel_id:
                    # محاولة استخدام المعرف المتوقع
                    channel_id = -1002349294062
                    print(f"{Colors.YELLOW}⚠️ استخدام المعرف المتوقع: {channel_id}{Colors.END}")
                
            except (InviteHashInvalid, InviteHashExpired):
                print(f"{Colors.RED}❌ رابط الدعوة منتهي الصلاحية أو غير صحيح{Colors.END}")
                return False
            except FloodWait as e:
                print(f"{Colors.RED}❌ FloodWait: انتظر {e.x} ثانية{Colors.END}")
                return False
            except Exception as e:
                print(f"{Colors.RED}❌ خطأ في الانضمام: {str(e)}{Colors.END}")
                return False
            
            # اختبار الوصول للرسالة
            if channel_id:
                print(f"\n{Colors.BLUE}🔄 اختبار الوصول للرسالة...{Colors.END}")
                msg_id = 526
                
                try:
                    message = await userbot.get_messages(channel_id, msg_id)
                    if message:
                        print(f"{Colors.GREEN}✅ تم العثور على الرسالة {msg_id}!{Colors.END}")
                        print(f"{Colors.CYAN}📊 نوع الرسالة: {message.media or 'نص'}{Colors.END}")
                        print(f"{Colors.CYAN}📊 المحتوى: {(message.text or message.caption or 'لا يوجد نص')[:50]}...{Colors.END}")
                        
                        if message.media:
                            print(f"{Colors.GREEN}✅ يمكن تحميل هذا الملف!{Colors.END}")
                            
                            # اختبار التحميل
                            print(f"{Colors.YELLOW}🔄 اختبار التحميل...{Colors.END}")
                            try:
                                file_path = await userbot.download_media(message, file_name="test_download")
                                if file_path:
                                    print(f"{Colors.GREEN}✅ تم التحميل بنجاح: {file_path}{Colors.END}")
                                    
                                    # حذف الملف المؤقت
                                    import os
                                    if os.path.exists(file_path):
                                        os.remove(file_path)
                                        print(f"{Colors.CYAN}🗑️ تم حذف الملف المؤقت{Colors.END}")
                                else:
                                    print(f"{Colors.RED}❌ فشل التحميل{Colors.END}")
                            except Exception as e:
                                print(f"{Colors.RED}❌ خطأ في التحميل: {str(e)}{Colors.END}")
                        else:
                            print(f"{Colors.YELLOW}⚠️ رسالة نصية فقط{Colors.END}")
                        
                        return True
                    else:
                        print(f"{Colors.RED}❌ لم يتم العثور على الرسالة {msg_id}{Colors.END}")
                        return False
                        
                except Exception as e:
                    print(f"{Colors.RED}❌ خطأ في الوصول للرسالة: {str(e)}{Colors.END}")
                    
                    # اقتراحات
                    print(f"\n{Colors.YELLOW}{Colors.BOLD}💡 الحلول المقترحة:{Colors.END}")
                    print(f"{Colors.CYAN}1. تأكد من أن الرسالة موجودة{Colors.END}")
                    print(f"{Colors.CYAN}2. تحقق من صلاحيات الحساب في القناة{Colors.END}")
                    print(f"{Colors.CYAN}3. جرب رقم رسالة مختلف{Colors.END}")
                    
                    return False
            else:
                print(f"{Colors.RED}❌ لم يتم العثور على معرف القناة{Colors.END}")
                return False
                
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ عام: {str(e)}{Colors.END}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        success = asyncio.run(test_join_and_access())
        
        if success:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 البوت يجب أن يعمل الآن مع القناة الخاصة!{Colors.END}")
            print(f"{Colors.CYAN}💡 جرب إرسال الرابط للبوت: https://t.me/c/2349294062/526{Colors.END}")
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}❌ هناك مشكلة في الوصول للقناة{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء الاختبار{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
