version: "3.3"

services:
  app:
    container_name: srcbot
    build: .
    command: bash bash.sh
    environment:
      API_ID: # Your API HASH from my.telegram.org
      API_HASH: # Your API ID from my.telegram.org
      BOT_TOKEN: # Bot token, get it from @BotFather 
      SESSION: # Pyrogram string session
      AUTH: # User ID of Bot owner
      FORCESUB: # Username name of public channel without using '@'
