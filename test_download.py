#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Download Tester
Test downloading from a specific channel
"""

import asyncio
from pyrogram import Client
from decouple import config
import re

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    DOWNLOAD TESTER                           ║
║              Test Channel Download                           ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def extract_channel_info(link):
    """استخراج معلومات القناة من الرابط"""
    patterns = [
        r"t\.me/c/(\d+)/(\d+)",  # Private channel
        r"t\.me/([^/]+)/(\d+)",  # Public channel
        r"telegram\.me/([^/]+)/(\d+)",  # Alternative domain
    ]
    
    for pattern in patterns:
        match = re.search(pattern, link)
        if match:
            return match.groups()
    return None, None

async def test_download(test_link):
    """اختبار تحميل من رابط معين"""
    print_banner()
    
    try:
        # قراءة المتغيرات
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        print(f"{Colors.YELLOW}🔄 اختبار التحميل من: {test_link}{Colors.END}")
        
        # استخراج معلومات الرابط
        channel, msg_id = extract_channel_info(test_link)
        if not channel or not msg_id:
            print(f"{Colors.RED}❌ رابط غير صحيح!{Colors.END}")
            return False
        
        print(f"{Colors.CYAN}📊 القناة: {channel}{Colors.END}")
        print(f"{Colors.CYAN}📊 معرف الرسالة: {msg_id}{Colors.END}")
        
        # إنشاء عميل المستخدم
        userbot = Client("test_download", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            try:
                # محاولة الحصول على الرسالة
                if channel.isdigit():
                    # قناة خاصة
                    chat_id = int(f"-100{channel}")
                    print(f"{Colors.YELLOW}🔄 محاولة الوصول للقناة الخاصة: {chat_id}{Colors.END}")
                else:
                    # قناة عامة
                    chat_id = channel
                    print(f"{Colors.YELLOW}🔄 محاولة الوصول للقناة العامة: @{channel}{Colors.END}")
                
                # الحصول على معلومات القناة
                try:
                    chat = await userbot.get_chat(chat_id)
                    print(f"{Colors.GREEN}✅ تم الوصول للقناة: {chat.title}{Colors.END}")
                    print(f"{Colors.CYAN}📊 نوع القناة: {chat.type}{Colors.END}")
                    print(f"{Colors.CYAN}📊 عدد الأعضاء: {chat.members_count or 'غير معروف'}{Colors.END}")
                except Exception as e:
                    print(f"{Colors.RED}❌ لا يمكن الوصول للقناة: {str(e)}{Colors.END}")
                    return False
                
                # الحصول على الرسالة
                try:
                    message = await userbot.get_messages(chat_id, int(msg_id))
                    if message:
                        print(f"{Colors.GREEN}✅ تم العثور على الرسالة!{Colors.END}")
                        print(f"{Colors.CYAN}📊 نوع الرسالة: {message.media}{Colors.END}")
                        print(f"{Colors.CYAN}📊 النص: {message.text or message.caption or 'لا يوجد نص'}{Colors.END}")
                        
                        if message.media:
                            print(f"{Colors.CYAN}📊 حجم الملف: {getattr(message.document, 'file_size', 'غير معروف') if message.document else 'غير معروف'}{Colors.END}")
                            print(f"{Colors.GREEN}✅ يمكن تحميل هذا الملف!{Colors.END}")
                        else:
                            print(f"{Colors.YELLOW}⚠️ الرسالة لا تحتوي على ملف{Colors.END}")
                        
                        return True
                    else:
                        print(f"{Colors.RED}❌ لم يتم العثور على الرسالة{Colors.END}")
                        return False
                        
                except Exception as e:
                    print(f"{Colors.RED}❌ خطأ في الحصول على الرسالة: {str(e)}{Colors.END}")
                    return False
                    
            except Exception as e:
                print(f"{Colors.RED}❌ خطأ عام: {str(e)}{Colors.END}")
                return False
            
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ في الإعداد: {str(e)}{Colors.END}")
        return False

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # أمثلة على روابط للاختبار
    test_links = [
        "https://t.me/durov/1",  # قناة عامة
        # يمكنك إضافة روابط أخرى هنا
    ]
    
    print(f"{Colors.YELLOW}أدخل رابط الرسالة للاختبار:{Colors.END}")
    print(f"{Colors.CYAN}مثال: https://t.me/channel_name/123{Colors.END}")
    
    try:
        test_link = input(f"{Colors.WHITE}الرابط: {Colors.END}").strip()
        
        if not test_link:
            print(f"{Colors.YELLOW}استخدام رابط افتراضي للاختبار...{Colors.END}")
            test_link = test_links[0]
        
        success = asyncio.run(test_download(test_link))
        
        if success:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 الاختبار نجح! البوت يجب أن يعمل مع هذا الرابط{Colors.END}")
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}❌ الاختبار فشل! هناك مشكلة في الوصول{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء الاختبار{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
