"""
Compatibility module for imghdr (removed in Python 3.13)
This provides basic image format detection using PIL/Pillow
"""

from PIL import Image
import io

def what(file, h=None):
    """Detect image format from file or bytes"""
    try:
        if isinstance(file, (str, bytes)):
            if isinstance(file, str):
                # It's a file path
                with open(file, 'rb') as f:
                    img = Image.open(f)
                    return img.format.lower() if img.format else None
            else:
                # It's bytes
                img = Image.open(io.BytesIO(file))
                return img.format.lower() if img.format else None
        else:
            # It's a file-like object
            img = Image.open(file)
            return img.format.lower() if img.format else None
    except Exception:
        return None

# Common format tests for compatibility
def test_jpeg(h, f):
    """JPEG format test"""
    return h[:4] == b'\xff\xd8\xff\xe0' or h[:4] == b'\xff\xd8\xff\xe1'

def test_png(h, f):
    """PNG format test"""
    return h.startswith(b'\x89PNG\r\n\x1a\n')

def test_gif(h, f):
    """GIF format test"""
    return h[:6] in (b'GIF87a', b'GIF89a')

def test_webp(h, f):
    """WebP format test"""
    return h[8:12] == b'WEBP'

def test_bmp(h, f):
    """BMP format test"""
    return h[:2] == b'BM'
