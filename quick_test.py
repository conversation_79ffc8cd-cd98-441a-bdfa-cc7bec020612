#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Quick Test for Channel Access
"""

import asyncio
from pyrogram import Client
from decouple import config

async def quick_test():
    """اختبار سريع للوصول للقناة"""
    
    try:
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        userbot = Client("quick_test", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            # معلومات الحساب
            me = await userbot.get_me()
            print(f"🤖 الحساب المستخدم: {me.first_name} (@{me.username or 'لا يوجد'})")
            
            # اختبار الوصول للقناة
            channel_id = -1002349294062
            
            try:
                chat = await userbot.get_chat(channel_id)
                print(f"✅ تم الوصول للقناة: {chat.title}")
                
                # اختبار قراءة رسالة
                message = await userbot.get_messages(channel_id, 526)
                if message:
                    print(f"✅ تم العثور على الرسالة 526!")
                    print(f"📊 النوع: {message.media or 'نص'}")
                    print(f"🎉 البوت سيعمل الآن!")
                else:
                    print(f"❌ الرسالة 526 غير موجودة")
                    
            except Exception as e:
                print(f"❌ لا يمكن الوصول للقناة: {str(e)}")
                print(f"💡 أضف الحساب @{me.username or me.id} لقناتك")
                
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    asyncio.run(quick_test())
