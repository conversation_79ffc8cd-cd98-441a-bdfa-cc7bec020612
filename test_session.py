#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Session Tester
Test if the userbot session is working properly
"""

import asyncio
from pyrogram import Client
from decouple import config

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    SESSION TESTER                            ║
║              Test Userbot Session                            ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

async def test_session():
    """اختبار جلسة المستخدم"""
    print_banner()
    
    try:
        # قراءة المتغيرات
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        print(f"{Colors.YELLOW}🔄 اختبار جلسة المستخدم...{Colors.END}")
        
        # إنشاء عميل المستخدم
        userbot = Client("test_session", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            # الحصول على معلومات المستخدم
            me = await userbot.get_me()
            
            print(f"\n{Colors.GREEN}{Colors.BOLD}✅ جلسة المستخدم تعمل!{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            print(f"{Colors.CYAN}👤 الاسم: {Colors.WHITE}{me.first_name} {me.last_name or ''}{Colors.END}")
            print(f"{Colors.CYAN}📱 رقم الهاتف: {Colors.WHITE}{me.phone_number or 'مخفي'}{Colors.END}")
            print(f"{Colors.CYAN}🆔 معرف المستخدم: {Colors.WHITE}{me.id}{Colors.END}")
            if me.username:
                print(f"{Colors.CYAN}👤 اسم المستخدم: {Colors.WHITE}@{me.username}{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            
            # اختبار الوصول لقناة عامة
            print(f"\n{Colors.YELLOW}🔄 اختبار الوصول للقنوات...{Colors.END}")
            
            test_channels = [
                "telegram",  # قناة تليجرام الرسمية
                "durov",     # قناة دوروف
            ]
            
            for channel in test_channels:
                try:
                    chat = await userbot.get_chat(channel)
                    print(f"{Colors.GREEN}✅ يمكن الوصول لـ @{channel}: {chat.title}{Colors.END}")
                    
                    # اختبار قراءة رسالة
                    try:
                        messages = []
                        async for message in userbot.get_chat_history(channel, limit=1):
                            messages.append(message)
                        if messages:
                            print(f"{Colors.GREEN}✅ يمكن قراءة الرسائل من @{channel}{Colors.END}")
                        else:
                            print(f"{Colors.YELLOW}⚠️ لا توجد رسائل في @{channel}{Colors.END}")
                    except Exception as e:
                        print(f"{Colors.RED}❌ لا يمكن قراءة الرسائل من @{channel}: {str(e)}{Colors.END}")
                        
                except Exception as e:
                    print(f"{Colors.RED}❌ لا يمكن الوصول لـ @{channel}: {str(e)}{Colors.END}")
            
            # اختبار إنشاء رابط دعوة (للقنوات الخاصة)
            print(f"\n{Colors.YELLOW}🔄 اختبار صلاحيات المستخدم...{Colors.END}")
            
            try:
                # الحصول على الدردشات
                dialogs = []
                async for dialog in userbot.get_dialogs(limit=5):
                    dialogs.append(dialog)
                
                print(f"{Colors.GREEN}✅ يمكن الوصول للدردشات: {len(dialogs)} دردشة{Colors.END}")
                
                # عرض بعض الدردشات
                for dialog in dialogs[:3]:
                    chat_type = "قناة" if dialog.chat.type.name == "CHANNEL" else "مجموعة" if dialog.chat.type.name in ["GROUP", "SUPERGROUP"] else "خاص"
                    print(f"{Colors.CYAN}  📁 {dialog.chat.title or dialog.chat.first_name}: {chat_type}{Colors.END}")
                    
            except Exception as e:
                print(f"{Colors.RED}❌ مشكلة في الوصول للدردشات: {str(e)}{Colors.END}")
            
            return True
            
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ في جلسة المستخدم: {str(e)}{Colors.END}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        success = asyncio.run(test_session())
        
        if success:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 جلسة المستخدم تعمل بشكل صحيح!{Colors.END}")
            print(f"{Colors.YELLOW}💡 إذا كان البوت لا يسحب، المشكلة قد تكون في:{Colors.END}")
            print(f"{Colors.CYAN}  1. القناة المستهدفة خاصة ولم تنضم إليها{Colors.END}")
            print(f"{Colors.CYAN}  2. القناة محظورة أو محذوفة{Colors.END}")
            print(f"{Colors.CYAN}  3. رابط القناة غير صحيح{Colors.END}")
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}❌ مشكلة في جلسة المستخدم!{Colors.END}")
            print(f"{Colors.YELLOW}💡 قد تحتاج لإنشاء جلسة جديدة{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء الاختبار{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
