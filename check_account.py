#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Check Account Details
"""

import asyncio
from pyrogram import Client
from decouple import config

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

async def check_account():
    """فحص تفاصيل الحساب المستخدم"""
    
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                    ACCOUNT CHECK                             ║")
    print("║              Check SESSION Account Details                   ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print(f"{Colors.END}")
    
    try:
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        userbot = Client("check_account", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            # معلومات الحساب
            me = await userbot.get_me()
            
            print(f"{Colors.BLUE}{Colors.BOLD}👤 معلومات الحساب المستخدم:{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            print(f"{Colors.CYAN}📛 الاسم: {Colors.WHITE}{me.first_name} {me.last_name or ''}{Colors.END}")
            print(f"{Colors.CYAN}🆔 معرف المستخدم: {Colors.WHITE}{me.id}{Colors.END}")
            if me.username:
                print(f"{Colors.CYAN}👤 اسم المستخدم: {Colors.WHITE}@{me.username}{Colors.END}")
            print(f"{Colors.CYAN}📱 رقم الهاتف: {Colors.WHITE}{me.phone_number or 'مخفي'}{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            
            # محاولة الوصول للقناة المحددة
            target_channel_id = -1002349294062
            
            print(f"\n{Colors.BLUE}{Colors.BOLD}🔍 محاولة الوصول لقناتك:{Colors.END}")
            print(f"{Colors.YELLOW}معرف القناة: {target_channel_id}{Colors.END}")
            
            try:
                # محاولة الحصول على معلومات القناة
                chat = await userbot.get_chat(target_channel_id)
                print(f"{Colors.GREEN}✅ تم الوصول للقناة: {chat.title}{Colors.END}")
                print(f"{Colors.CYAN}📊 نوع القناة: {chat.type}{Colors.END}")
                print(f"{Colors.CYAN}📊 عدد الأعضاء: {chat.members_count or 'غير معروف'}{Colors.END}")
                
                # التحقق من الصلاحيات
                try:
                    member = await userbot.get_chat_member(target_channel_id, me.id)
                    print(f"{Colors.GREEN}✅ أنت عضو في القناة{Colors.END}")
                    print(f"{Colors.CYAN}📊 صلاحياتك: {member.status}{Colors.END}")
                    
                    # اختبار قراءة رسالة
                    try:
                        message = await userbot.get_messages(target_channel_id, 526)
                        if message:
                            print(f"{Colors.GREEN}✅ يمكن قراءة الرسالة 526!{Colors.END}")
                            print(f"{Colors.CYAN}📊 نوع الرسالة: {message.media or 'نص'}{Colors.END}")
                            return True
                        else:
                            print(f"{Colors.RED}❌ الرسالة 526 غير موجودة{Colors.END}")
                    except Exception as e:
                        print(f"{Colors.RED}❌ لا يمكن قراءة الرسالة: {str(e)}{Colors.END}")
                        
                except Exception as e:
                    print(f"{Colors.RED}❌ لست عضواً في القناة: {str(e)}{Colors.END}")
                    
            except Exception as e:
                print(f"{Colors.RED}❌ لا يمكن الوصول للقناة: {str(e)}{Colors.END}")
                
                # اقتراحات
                print(f"\n{Colors.YELLOW}{Colors.BOLD}💡 الحلول:{Colors.END}")
                print(f"{Colors.CYAN}1. أضف هذا الحساب (@{me.username or me.id}) كمشرف في قناتك{Colors.END}")
                print(f"{Colors.CYAN}2. أو انضم للقناة بهذا الحساب{Colors.END}")
                print(f"{Colors.CYAN}3. تأكد من أن القناة لا تزال موجودة{Colors.END}")
                
            # عرض القنوات التي يملكها
            print(f"\n{Colors.BLUE}{Colors.BOLD}👑 القنوات التي تملكها:{Colors.END}")
            owned_channels = []
            
            async for dialog in userbot.get_dialogs(limit=100):
                if dialog.chat.type.name == "CHANNEL":
                    try:
                        member = await userbot.get_chat_member(dialog.chat.id, me.id)
                        if member.status.name in ["OWNER", "ADMINISTRATOR"]:
                            owned_channels.append((dialog.chat, member.status.name))
                    except:
                        pass
            
            if owned_channels:
                for i, (chat, status) in enumerate(owned_channels[:10], 1):
                    chat_id = str(chat.id).replace('-100', '') if str(chat.id).startswith('-100') else str(chat.id)
                    status_ar = "مالك" if status == "OWNER" else "مشرف"
                    print(f"{Colors.CYAN}{i}. {chat.title} ({status_ar}) - ID: {chat_id}{Colors.END}")
                    
                    # التحقق من القناة المطلوبة
                    if chat_id == "2349294062":
                        print(f"{Colors.GREEN}   ✅ هذه هي قناتك المطلوبة!{Colors.END}")
                        return True
            else:
                print(f"{Colors.YELLOW}⚠️ لا توجد قنوات تملكها أو تديرها{Colors.END}")
                
            return False
            
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ: {str(e)}{Colors.END}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        success = asyncio.run(check_account())
        
        if success:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 البوت يجب أن يعمل الآن!{Colors.END}")
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}❌ تحتاج لإضافة حسابك للقناة{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء الفحص{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
