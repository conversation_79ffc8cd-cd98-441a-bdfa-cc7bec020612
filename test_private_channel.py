#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Private Channel Access
"""

import asyncio
from pyrogram import Client
from decouple import config

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

async def test_private_channel():
    """اختبار الوصول للقناة الخاصة"""
    
    # الرابط المحدد
    test_link = "https://t.me/c/2349294062/526"
    channel_id = -1002349294062  # تحويل لمعرف القناة الصحيح
    msg_id = 526
    
    print(f"{Colors.CYAN}{Colors.BOLD}🔍 اختبار القناة الخاصة{Colors.END}")
    print(f"{Colors.YELLOW}الرابط: {test_link}{Colors.END}")
    print(f"{Colors.YELLOW}معرف القناة: {channel_id}{Colors.END}")
    print(f"{Colors.YELLOW}معرف الرسالة: {msg_id}{Colors.END}")
    
    try:
        # قراءة المتغيرات
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        # إنشاء عميل المستخدم
        userbot = Client("test_private", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            print(f"\n{Colors.BLUE}🔄 محاولة الوصول للقناة...{Colors.END}")
            
            try:
                # محاولة الحصول على معلومات القناة
                chat = await userbot.get_chat(channel_id)
                print(f"{Colors.GREEN}✅ تم الوصول للقناة: {chat.title}{Colors.END}")
                print(f"{Colors.CYAN}📊 نوع القناة: {chat.type}{Colors.END}")
                print(f"{Colors.CYAN}📊 عدد الأعضاء: {chat.members_count or 'غير معروف'}{Colors.END}")
                
                # محاولة الحصول على الرسالة
                try:
                    message = await userbot.get_messages(channel_id, msg_id)
                    if message:
                        print(f"{Colors.GREEN}✅ تم العثور على الرسالة!{Colors.END}")
                        print(f"{Colors.CYAN}📊 نوع الرسالة: {message.media or 'نص'}{Colors.END}")
                        print(f"{Colors.CYAN}📊 المحتوى: {(message.text or message.caption or 'لا يوجد نص')[:100]}...{Colors.END}")
                        
                        if message.media:
                            print(f"{Colors.GREEN}✅ البوت يجب أن يتمكن من تحميل هذا الملف!{Colors.END}")
                        else:
                            print(f"{Colors.YELLOW}⚠️ رسالة نصية فقط{Colors.END}")
                        
                        return True
                    else:
                        print(f"{Colors.RED}❌ لم يتم العثور على الرسالة{Colors.END}")
                        return False
                        
                except Exception as e:
                    print(f"{Colors.RED}❌ خطأ في الحصول على الرسالة: {str(e)}{Colors.END}")
                    return False
                    
            except Exception as e:
                print(f"{Colors.RED}❌ لا يمكن الوصول للقناة: {str(e)}{Colors.END}")
                
                # اقتراحات للحل
                print(f"\n{Colors.YELLOW}{Colors.BOLD}💡 الحلول المقترحة:{Colors.END}")
                print(f"{Colors.CYAN}1. تأكد من انضمامك للقناة بحسابك الشخصي{Colors.END}")
                print(f"{Colors.CYAN}2. احصل على رابط دعوة القناة وانضم إليها{Colors.END}")
                print(f"{Colors.CYAN}3. تأكد من أن القناة لا تزال موجودة{Colors.END}")
                print(f"{Colors.CYAN}4. تحقق من أن الرابط صحيح{Colors.END}")
                
                return False
                
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ في الإعداد: {str(e)}{Colors.END}")
        return False

async def check_user_channels():
    """فحص القنوات التي انضم إليها المستخدم"""
    
    print(f"\n{Colors.BLUE}{Colors.BOLD}📋 فحص القنوات المنضم إليها:{Colors.END}")
    
    try:
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        SESSION = config("SESSION")
        
        userbot = Client("check_channels", session_string=SESSION, api_hash=API_HASH, api_id=API_ID)
        
        async with userbot:
            print(f"{Colors.YELLOW}🔄 جاري فحص القنوات...{Colors.END}")
            
            channels_found = []
            private_channels = []
            
            async for dialog in userbot.get_dialogs(limit=50):
                if dialog.chat.type.name == "CHANNEL":
                    channels_found.append(dialog)
                    if dialog.chat.username is None:  # قناة خاصة
                        private_channels.append(dialog)
            
            print(f"{Colors.GREEN}✅ تم العثور على {len(channels_found)} قناة{Colors.END}")
            print(f"{Colors.CYAN}📊 منها {len(private_channels)} قناة خاصة{Colors.END}")
            
            if private_channels:
                print(f"\n{Colors.PURPLE}📋 القنوات الخاصة المنضم إليها:{Colors.END}")
                for i, dialog in enumerate(private_channels[:10], 1):
                    chat_id = str(dialog.chat.id).replace('-100', '')
                    print(f"{Colors.CYAN}{i}. {dialog.chat.title} (ID: {chat_id}){Colors.END}")
                    
                    # التحقق من القناة المطلوبة
                    if chat_id == "2349294062":
                        print(f"{Colors.GREEN}   ✅ هذه هي القناة المطلوبة!{Colors.END}")
                        return True
                
                print(f"\n{Colors.RED}❌ القناة المطلوبة (2349294062) غير موجودة في قائمة القنوات{Colors.END}")
            else:
                print(f"{Colors.YELLOW}⚠️ لا توجد قنوات خاصة منضم إليها{Colors.END}")
            
            return False
            
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ في فحص القنوات: {str(e)}{Colors.END}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        print(f"{Colors.CYAN}{Colors.BOLD}")
        print("╔══════════════════════════════════════════════════════════════╗")
        print("║                 PRIVATE CHANNEL TEST                         ║")
        print("║              Test Access to Private Channel                  ║")
        print("╚══════════════════════════════════════════════════════════════╝")
        print(f"{Colors.END}")
        
        # فحص القنوات المنضم إليها
        channel_found = asyncio.run(check_user_channels())
        
        if channel_found:
            # اختبار الوصول للرسالة
            success = asyncio.run(test_private_channel())
            if success:
                print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 البوت يجب أن يعمل مع هذا الرابط!{Colors.END}")
            else:
                print(f"\n{Colors.RED}{Colors.BOLD}❌ هناك مشكلة في الوصول للرسالة{Colors.END}")
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}❌ أنت غير منضم لهذه القناة الخاصة{Colors.END}")
            print(f"{Colors.YELLOW}💡 احصل على رابط دعوة القناة وانضم إليها أولاً{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء الاختبار{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
