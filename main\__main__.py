import glob
from pathlib import Path
from main.utils import load_plugins
import logging
from . import bot

logging.basicConfig(format='[%(levelname) 5s/%(asctime)s] %(name)s: %(message)s',
                    level=logging.WARNING)

path = "main/plugins/*.py"
files = glob.glob(path)
for name in files:
    with open(name) as a:
        patt = Path(a.name)
        plugin_name = patt.stem
        load_plugins(plugin_name.replace(".py", ""))

#Don't be a thief 
print("Successfully deployed!")
print("By <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> • DroneBots")

if __name__ == "__main__":
    bot.run_until_disconnected()
