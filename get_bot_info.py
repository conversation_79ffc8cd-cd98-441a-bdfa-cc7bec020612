#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Bot Info Checker
Get bot information using bot token
"""

import asyncio
from pyrogram import Client
from decouple import config

# ألوان للنص
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    BOT INFO CHECKER                          ║
║              Save Restricted Content Bot                     ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

async def get_bot_info():
    """الحصول على معلومات البوت"""
    print_banner()
    
    try:
        # قراءة المتغيرات من .env
        API_ID = config("API_ID", cast=int)
        API_HASH = config("API_HASH")
        BOT_TOKEN = config("BOT_TOKEN")
        FORCESUB = config("FORCESUB", default="")
        
        print(f"{Colors.YELLOW}🔄 جاري الحصول على معلومات البوت...{Colors.END}")
        
        # إنشاء عميل البوت
        bot = Client("bot_info", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)
        
        async with bot:
            # الحصول على معلومات البوت
            me = await bot.get_me()
            
            print(f"\n{Colors.GREEN}{Colors.BOLD}🤖 معلومات البوت:{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            print(f"{Colors.CYAN}📛 اسم البوت: {Colors.WHITE}{me.first_name}{Colors.END}")
            if me.last_name:
                print(f"{Colors.CYAN}📛 اسم العائلة: {Colors.WHITE}{me.last_name}{Colors.END}")
            print(f"{Colors.CYAN}👤 اسم المستخدم: {Colors.WHITE}@{me.username}{Colors.END}")
            print(f"{Colors.CYAN}🆔 معرف البوت: {Colors.WHITE}{me.id}{Colors.END}")
            if hasattr(me, 'bio') and me.bio:
                print(f"{Colors.CYAN}📝 الوصف: {Colors.WHITE}{me.bio}{Colors.END}")
            print(f"{Colors.PURPLE}{'='*60}{Colors.END}")
            
            # رابط البوت
            bot_link = f"https://t.me/{me.username}"
            print(f"{Colors.YELLOW}🔗 رابط البوت: {Colors.WHITE}{bot_link}{Colors.END}")
            
            # معلومات القناة الإجبارية
            if FORCESUB:
                print(f"\n{Colors.GREEN}{Colors.BOLD}📢 القناة الإجبارية:{Colors.END}")
                print(f"{Colors.CYAN}📛 اسم القناة: {Colors.WHITE}@{FORCESUB}{Colors.END}")
                print(f"{Colors.CYAN}🔗 رابط القناة: {Colors.WHITE}https://t.me/{FORCESUB}{Colors.END}")
                
                try:
                    # محاولة الحصول على معلومات القناة
                    channel = await bot.get_chat(FORCESUB)
                    print(f"{Colors.CYAN}📊 عدد الأعضاء: {Colors.WHITE}{channel.members_count if channel.members_count else 'غير معروف'}{Colors.END}")
                    if channel.description:
                        print(f"{Colors.CYAN}📝 وصف القناة: {Colors.WHITE}{channel.description[:100]}...{Colors.END}")
                except Exception as e:
                    print(f"{Colors.RED}⚠️ لا يمكن الوصول لمعلومات القناة: {str(e)}{Colors.END}")
            else:
                print(f"\n{Colors.YELLOW}📢 لا توجد قناة إجبارية مفعلة{Colors.END}")
            
            print(f"\n{Colors.GREEN}{Colors.BOLD}✅ تم الحصول على المعلومات بنجاح!{Colors.END}")
            
            return me.username, FORCESUB
            
    except Exception as e:
        print(f"{Colors.RED}❌ حدث خطأ: {str(e)}{Colors.END}")
        return None, None

def main():
    """الدالة الرئيسية"""
    try:
        username, channel = asyncio.run(get_bot_info())
        
        if username:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎯 ملخص سريع:{Colors.END}")
            print(f"{Colors.CYAN}🤖 البوت: {Colors.WHITE}@{username}{Colors.END}")
            if channel:
                print(f"{Colors.CYAN}📢 القناة: {Colors.WHITE}@{channel}{Colors.END}")
            print(f"\n{Colors.YELLOW}💡 يمكنك الآن البحث عن البوت في تليجرام!{Colors.END}")
        else:
            print(f"{Colors.RED}❌ فشل في الحصول على معلومات البوت{Colors.END}")
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⏹️ تم إلغاء العملية بواسطة المستخدم{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ خطأ غير متوقع: {str(e)}{Colors.END}")

if __name__ == "__main__":
    main()
